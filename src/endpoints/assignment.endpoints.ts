import { Endpoint } from 'payload';
import { SubmissionController } from '../controllers/submission.controller';
import { AssignmentController } from '@/controllers/assignment.controller';

export const endpoints: Omit<Endpoint, 'root'>[] = [
  {
    path: '/:id/submit',
    method: 'post',
    handler: SubmissionController.handleSubmission,
  },
  {
    path: '/generate',
    method: 'post',
    handler: AssignmentController.handleGeneration,
  },
  {
    path: '/:id/download-report',
    method: 'get',
    handler: SubmissionController.handleDownloadReport,
  },
];
