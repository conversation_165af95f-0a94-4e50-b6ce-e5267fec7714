/* tslint:disable */
/* eslint-disable */
/**
 * This file was automatically generated by Payload.
 * DO NOT MODIFY IT BY HAND. Instead, modify your source Payload config,
 * and re-run `payload generate:types` to regenerate this file.
 */

/**
 * Supported timezones in IANA format.
 *
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "supportedTimezones".
 */
export type SupportedTimezones =
  | 'Pacific/Midway'
  | 'Pacific/Niue'
  | 'Pacific/Honolulu'
  | 'Pacific/Rarotonga'
  | 'America/Anchorage'
  | 'Pacific/Gambier'
  | 'America/Los_Angeles'
  | 'America/Tijuana'
  | 'America/Denver'
  | 'America/Phoenix'
  | 'America/Chicago'
  | 'America/Guatemala'
  | 'America/New_York'
  | 'America/Bogota'
  | 'America/Caracas'
  | 'America/Santiago'
  | 'America/Buenos_Aires'
  | 'America/Sao_Paulo'
  | 'Atlantic/South_Georgia'
  | 'Atlantic/Azores'
  | 'Atlantic/Cape_Verde'
  | 'Europe/London'
  | 'Europe/Berlin'
  | 'Africa/Lagos'
  | 'Europe/Athens'
  | 'Africa/Cairo'
  | 'Europe/Moscow'
  | 'Asia/Riyadh'
  | 'Asia/Dubai'
  | 'Asia/Baku'
  | 'Asia/Karachi'
  | 'Asia/Tashkent'
  | 'Asia/Calcutta'
  | 'Asia/Dhaka'
  | 'Asia/Almaty'
  | 'Asia/Jakarta'
  | 'Asia/Bangkok'
  | 'Asia/Shanghai'
  | 'Asia/Singapore'
  | 'Asia/Tokyo'
  | 'Asia/Seoul'
  | 'Australia/Brisbane'
  | 'Australia/Sydney'
  | 'Pacific/Guam'
  | 'Pacific/Noumea'
  | 'Pacific/Auckland'
  | 'Pacific/Fiji';

export interface Config {
  auth: {
    users: UserAuthOperations;
  };
  blocks: {};
  collections: {
    users: User;
    tenants: Tenant;
    batches: Batch;
    enrollments: Enrollment;
    subjects: Subject;
    modules: Module;
    assignments: Assignment;
    submissions: Submission;
    'payload-jobs': PayloadJob;
    'payload-locked-documents': PayloadLockedDocument;
    'payload-preferences': PayloadPreference;
    'payload-migrations': PayloadMigration;
  };
  collectionsJoins: {
    assignments: {
      submissions: 'submissions';
    };
  };
  collectionsSelect: {
    users: UsersSelect<false> | UsersSelect<true>;
    tenants: TenantsSelect<false> | TenantsSelect<true>;
    batches: BatchesSelect<false> | BatchesSelect<true>;
    enrollments: EnrollmentsSelect<false> | EnrollmentsSelect<true>;
    subjects: SubjectsSelect<false> | SubjectsSelect<true>;
    modules: ModulesSelect<false> | ModulesSelect<true>;
    assignments: AssignmentsSelect<false> | AssignmentsSelect<true>;
    submissions: SubmissionsSelect<false> | SubmissionsSelect<true>;
    'payload-jobs': PayloadJobsSelect<false> | PayloadJobsSelect<true>;
    'payload-locked-documents':
      | PayloadLockedDocumentsSelect<false>
      | PayloadLockedDocumentsSelect<true>;
    'payload-preferences': PayloadPreferencesSelect<false> | PayloadPreferencesSelect<true>;
    'payload-migrations': PayloadMigrationsSelect<false> | PayloadMigrationsSelect<true>;
  };
  db: {
    defaultIDType: number;
  };
  globals: {};
  globalsSelect: {};
  locale: null;
  user: User & {
    collection: 'users';
  };
  jobs: {
    tasks: {
      'submission-worker': TaskSubmissionWorker;
      inline: {
        input: unknown;
        output: unknown;
      };
    };
    workflows: unknown;
  };
}
export interface UserAuthOperations {
  forgotPassword: {
    email: string;
    password: string;
  };
  login: {
    email: string;
    password: string;
  };
  registerFirstUser: {
    email: string;
    password: string;
  };
  unlock: {
    email: string;
    password: string;
  };
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "users".
 */
export interface User {
  id: number;
  roles?: ('super-admin' | 'user')[] | null;
  username?: string | null;
  fullName?: string | null;
  division?: string | null;
  tenants?:
    | {
        tenant: number | Tenant;
        roles: ('faculty' | 'student')[];
        id?: string | null;
      }[]
    | null;
  updatedAt: string;
  createdAt: string;
  email: string;
  resetPasswordToken?: string | null;
  resetPasswordExpiration?: string | null;
  salt?: string | null;
  hash?: string | null;
  loginAttempts?: number | null;
  lockUntil?: string | null;
  sessions?:
    | {
        id: string;
        createdAt?: string | null;
        expiresAt: string;
      }[]
    | null;
  password?: string | null;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "tenants".
 */
export interface Tenant {
  id: number;
  name: string;
  /**
   * Used for domain-based tenant handling
   */
  domain?: string | null;
  slug: string;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "batches".
 */
export interface Batch {
  id: number;
  batchId: string;
  students?: (number | User)[] | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "enrollments".
 */
export interface Enrollment {
  id: number;
  module: number | Module;
  batch: number | Batch;
  accessStart: string;
  accessEnd: string;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "modules".
 */
export interface Module {
  id: number;
  title: string;
  assignments?: (number | Assignment)[] | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "assignments".
 */
export interface Assignment {
  id: number;
  tenant?: (number | null) | Tenant;
  title: string;
  subject?: (number | null) | Subject;
  module?: (number | null) | Module;
  description?: string | null;
  language: 'web' | 'java' | 'c';
  difficulty: 'easy' | 'medium' | 'hard';
  points: number;
  dueDate: string;
  /**
   * Detailed instructions for completing the assignment
   */
  instructions?: string | null;
  testsRequirement?: string | null;
  /**
   * Provide helpful hints for common problems
   */
  hints?:
    | {
        question: string;
        answer: string;
        id?: string | null;
      }[]
    | null;
  /**
   * Provide helpful resources for completing the assignment
   */
  resources?:
    | {
        title: string;
        url: string;
        id?: string | null;
      }[]
    | null;
  starterCode?: {
    html?: string | null;
    css?: string | null;
    js?: string | null;
    java?: string | null;
    c?: string | null;
  };
  solutionCode?: {
    html?: string | null;
    css?: string | null;
    js?: string | null;
    java?: string | null;
    c?: string | null;
  };
  /**
   * Add notes about the solution (visible only to instructors)
   */
  solutionNotes?: string | null;
  /**
   * View all submissions for this assignment
   */
  submissions?: {
    docs?: (number | Submission)[];
    hasNextPage?: boolean;
    totalDocs?: number;
  };
  updatedAt: string;
  createdAt: string;
  _status?: ('draft' | 'published') | null;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "subjects".
 */
export interface Subject {
  id: number;
  tenant?: (number | null) | Tenant;
  name: string;
  description?: string | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "submissions".
 */
export interface Submission {
  id: number;
  tenant?: (number | null) | Tenant;
  student: number | User;
  assignment: number | Assignment;
  html?: string | null;
  css?: string | null;
  js?: string | null;
  java?: string | null;
  c?: string | null;
  summary?: string | null;
  status?: ('review' | 'graded' | 'resubmit') | null;
  score?: number | null;
  testsResult?:
    | {
        input?: string | null;
        expected?: string | null;
        actual?: string | null;
        status?: ('PASS' | 'FAIL') | null;
        id?: string | null;
      }[]
    | null;
  feedback?: string | null;
  /**
   * Prevents further edits by the student.
   */
  isLocked?: boolean | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "payload-jobs".
 */
export interface PayloadJob {
  id: number;
  /**
   * Input data provided to the job
   */
  input?:
    | {
        [k: string]: unknown;
      }
    | unknown[]
    | string
    | number
    | boolean
    | null;
  taskStatus?:
    | {
        [k: string]: unknown;
      }
    | unknown[]
    | string
    | number
    | boolean
    | null;
  completedAt?: string | null;
  totalTried?: number | null;
  /**
   * If hasError is true this job will not be retried
   */
  hasError?: boolean | null;
  /**
   * If hasError is true, this is the error that caused it
   */
  error?:
    | {
        [k: string]: unknown;
      }
    | unknown[]
    | string
    | number
    | boolean
    | null;
  /**
   * Task execution log
   */
  log?:
    | {
        executedAt: string;
        completedAt: string;
        taskSlug: 'inline' | 'submission-worker';
        taskID: string;
        input?:
          | {
              [k: string]: unknown;
            }
          | unknown[]
          | string
          | number
          | boolean
          | null;
        output?:
          | {
              [k: string]: unknown;
            }
          | unknown[]
          | string
          | number
          | boolean
          | null;
        state: 'failed' | 'succeeded';
        error?:
          | {
              [k: string]: unknown;
            }
          | unknown[]
          | string
          | number
          | boolean
          | null;
        id?: string | null;
      }[]
    | null;
  taskSlug?: ('inline' | 'submission-worker') | null;
  queue?: string | null;
  waitUntil?: string | null;
  processing?: boolean | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "payload-locked-documents".
 */
export interface PayloadLockedDocument {
  id: number;
  document?:
    | ({
        relationTo: 'users';
        value: number | User;
      } | null)
    | ({
        relationTo: 'tenants';
        value: number | Tenant;
      } | null)
    | ({
        relationTo: 'batches';
        value: number | Batch;
      } | null)
    | ({
        relationTo: 'enrollments';
        value: number | Enrollment;
      } | null)
    | ({
        relationTo: 'subjects';
        value: number | Subject;
      } | null)
    | ({
        relationTo: 'modules';
        value: number | Module;
      } | null)
    | ({
        relationTo: 'assignments';
        value: number | Assignment;
      } | null)
    | ({
        relationTo: 'submissions';
        value: number | Submission;
      } | null)
    | ({
        relationTo: 'payload-jobs';
        value: number | PayloadJob;
      } | null);
  globalSlug?: string | null;
  user: {
    relationTo: 'users';
    value: number | User;
  };
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "payload-preferences".
 */
export interface PayloadPreference {
  id: number;
  user: {
    relationTo: 'users';
    value: number | User;
  };
  key?: string | null;
  value?:
    | {
        [k: string]: unknown;
      }
    | unknown[]
    | string
    | number
    | boolean
    | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "payload-migrations".
 */
export interface PayloadMigration {
  id: number;
  name?: string | null;
  batch?: number | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "users_select".
 */
export interface UsersSelect<T extends boolean = true> {
  roles?: T;
  username?: T;
  fullName?: T;
  division?: T;
  tenants?:
    | T
    | {
        tenant?: T;
        roles?: T;
        id?: T;
      };
  updatedAt?: T;
  createdAt?: T;
  email?: T;
  resetPasswordToken?: T;
  resetPasswordExpiration?: T;
  salt?: T;
  hash?: T;
  loginAttempts?: T;
  lockUntil?: T;
  sessions?:
    | T
    | {
        id?: T;
        createdAt?: T;
        expiresAt?: T;
      };
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "tenants_select".
 */
export interface TenantsSelect<T extends boolean = true> {
  name?: T;
  domain?: T;
  slug?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "batches_select".
 */
export interface BatchesSelect<T extends boolean = true> {
  batchId?: T;
  students?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "enrollments_select".
 */
export interface EnrollmentsSelect<T extends boolean = true> {
  module?: T;
  batch?: T;
  accessStart?: T;
  accessEnd?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "subjects_select".
 */
export interface SubjectsSelect<T extends boolean = true> {
  tenant?: T;
  name?: T;
  description?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "modules_select".
 */
export interface ModulesSelect<T extends boolean = true> {
  title?: T;
  assignments?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "assignments_select".
 */
export interface AssignmentsSelect<T extends boolean = true> {
  tenant?: T;
  title?: T;
  subject?: T;
  module?: T;
  description?: T;
  language?: T;
  difficulty?: T;
  points?: T;
  dueDate?: T;
  instructions?: T;
  testsRequirement?: T;
  hints?:
    | T
    | {
        question?: T;
        answer?: T;
        id?: T;
      };
  resources?:
    | T
    | {
        title?: T;
        url?: T;
        id?: T;
      };
  starterCode?:
    | T
    | {
        html?: T;
        css?: T;
        js?: T;
        java?: T;
        c?: T;
      };
  solutionCode?:
    | T
    | {
        html?: T;
        css?: T;
        js?: T;
        java?: T;
        c?: T;
      };
  solutionNotes?: T;
  submissions?: T;
  updatedAt?: T;
  createdAt?: T;
  _status?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "submissions_select".
 */
export interface SubmissionsSelect<T extends boolean = true> {
  tenant?: T;
  student?: T;
  assignment?: T;
  html?: T;
  css?: T;
  js?: T;
  java?: T;
  c?: T;
  summary?: T;
  status?: T;
  score?: T;
  testsResult?:
    | T
    | {
        input?: T;
        expected?: T;
        actual?: T;
        status?: T;
        id?: T;
      };
  feedback?: T;
  isLocked?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "payload-jobs_select".
 */
export interface PayloadJobsSelect<T extends boolean = true> {
  input?: T;
  taskStatus?: T;
  completedAt?: T;
  totalTried?: T;
  hasError?: T;
  error?: T;
  log?:
    | T
    | {
        executedAt?: T;
        completedAt?: T;
        taskSlug?: T;
        taskID?: T;
        input?: T;
        output?: T;
        state?: T;
        error?: T;
        id?: T;
      };
  taskSlug?: T;
  queue?: T;
  waitUntil?: T;
  processing?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "payload-locked-documents_select".
 */
export interface PayloadLockedDocumentsSelect<T extends boolean = true> {
  document?: T;
  globalSlug?: T;
  user?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "payload-preferences_select".
 */
export interface PayloadPreferencesSelect<T extends boolean = true> {
  user?: T;
  key?: T;
  value?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "payload-migrations_select".
 */
export interface PayloadMigrationsSelect<T extends boolean = true> {
  name?: T;
  batch?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "TaskSubmission-worker".
 */
export interface TaskSubmissionWorker {
  input: {
    submissionId: string;
  };
  output: {
    resultId?: string | null;
  };
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "auth".
 */
export interface Auth {
  [k: string]: unknown;
}

declare module 'payload' {
  export interface GeneratedTypes extends Config {}
}
