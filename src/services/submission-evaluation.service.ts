import type { BasePayload } from 'payload';
import { compileCode, submitCode } from '@/lib/judge';
import { getCurrentModelName } from '@/mastra/config';
import {
  createSummaryAgent,
  createTestsGeneratorAgent,
} from '@/mastra/workflows/auto-evaluate-workflow/agents';
import {
  summaryGenerationOutputSchema,
  testsGenerationOutputSchema,
} from '@/mastra/workflows/auto-evaluate-workflow/schema';
import type { Assignment } from '@/payload-types';

interface TimingData {
  submissionId: string;
  timestamp: string;
  fetchTime: number;
  compilationTime: number;
  testGenTime?: number;
  testExecTime?: number;
  summaryTime?: number;
  updateTime: number;
  totalTime: number;
  currentModel: string;
  status:
    | 'success'
    | 'compilation_failed'
    | 'test_gen_failed'
    | 'test_exec_failed'
    | 'summary_failed';
}

interface EvaluationResult {
  testsResult: any[];
  summary: string;
  feedback: string;
  score: number;
}

export class SubmissionEvaluationService {
  private payload: BasePayload;
  private testsGeneratorAgent: ReturnType<typeof createTestsGeneratorAgent>;
  private summaryAgent: ReturnType<typeof createSummaryAgent>;

  constructor(payload: BasePayload) {
    this.payload = payload;
    this.testsGeneratorAgent = createTestsGeneratorAgent();
    this.summaryAgent = createSummaryAgent();
  }

  async evaluateSubmission(submissionId: string): Promise<{ output: any }> {
    const taskStartTime = Date.now();
    console.log(`[TIMING] Task started for submission ${submissionId}`);

    // Initialize timing variables
    let fetchTime = 0;
    let compilationTime = 0;
    let testGenTime: number | undefined;
    let testExecTime: number | undefined;
    let summaryTime: number | undefined;
    let updateTime = 0;
    let status: TimingData['status'] = 'success';

    try {
      // Step 1: Fetch submission data
      const { submission, fetchTime: ft } = await this.fetchSubmissionData(submissionId);
      fetchTime = ft;

      const assignment = submission.assignment as Assignment;
      const sourceCode = this.extractSourceCode(submission, assignment.language);

      if (!sourceCode?.trim()) {
        throw new Error('No source code provided');
      }

      // Step 2: Check compilation
      const { compilationTime: ct } = await this.checkCompilation(sourceCode, assignment.language);
      compilationTime = ct;

      // Step 3: Evaluate submission
      const evaluationResult = await this.performEvaluation(submission, assignment, sourceCode);

      if (evaluationResult.testGenTime) testGenTime = evaluationResult.testGenTime;
      if (evaluationResult.testExecTime) testExecTime = evaluationResult.testExecTime;
      if (evaluationResult.summaryTime) summaryTime = evaluationResult.summaryTime;

      // Step 4: Update submission
      const { updateTime: ut } = await this.updateSubmission(submissionId, evaluationResult);
      updateTime = ut;

      // Log timing data
      const totalTime = Date.now() - taskStartTime;
      this.logTimingData({
        submissionId,
        timestamp: new Date().toISOString(),
        fetchTime,
        compilationTime,
        testGenTime,
        testExecTime,
        summaryTime,
        updateTime,
        totalTime,
        currentModel: getCurrentModelName(),
        status,
      });

      console.log(`[TIMING] Total task completed in ${totalTime}ms for submission ${submissionId}`);

      return {
        output: {
          resultId: submission.id,
        },
      };
    } catch (error) {
      if (error instanceof CompilationError) {
        status = 'compilation_failed';
        await this.handleCompilationError(submissionId, error);
        return {
          output: { message: 'Compilation failed', error: error.error },
        };
      }

      if (error instanceof TestGenerationError) {
        status = 'test_gen_failed';
        await this.handleTestGenerationError(submissionId, error);
        return {
          output: {
            message: 'Test case generation failed',
            error: error.error,
          },
        };
      }

      if (error instanceof TestExecutionError) {
        status = 'test_exec_failed';
        await this.handleTestExecutionError(submissionId, error);
        return {
          output: { message: 'Test execution failed', error: error.error },
        };
      }

      throw error;
    }
  }

  private async fetchSubmissionData(submissionId: string) {
    const fetchStartTime = Date.now();

    const submission = await this.payload.findByID({
      collection: 'submissions',
      id: submissionId,
      select: {
        assignment: true,
        solutionCode: true,
        html: true,
        css: true,
        js: true,
        c: true,
        java: true,
      },
      populate: {
        assignments: {
          language: true,
          testsRequirement: true,
          title: true,
          description: true,
        },
      },
    });

    const fetchTime = Date.now() - fetchStartTime;
    console.log(`[TIMING] Submission data fetch completed in ${fetchTime}ms`);

    return { submission, fetchTime };
  }

  private extractSourceCode(submission: any, language: string): string {
    return (submission?.[language as keyof typeof submission] ?? null) as string;
  }

  private async checkCompilation(sourceCode: string, language: string) {
    const compilationStartTime = Date.now();
    const languageId = language === 'java' ? 4 : 1;

    try {
      console.log('Checking code compilation...');
      await compileCode(sourceCode, languageId);
      const compilationTime = Date.now() - compilationStartTime;
      console.log(`[TIMING] Code compilation successful in ${compilationTime}ms`);
      return { compilationTime };
    } catch (compilationError) {
      const compilationTime = Date.now() - compilationStartTime;
      console.log(`[TIMING] Code compilation failed in ${compilationTime}ms`);
      console.error('Compilation failed:', compilationError);

      throw new CompilationError(
        'Compilation failed',
        compilationError instanceof Error ? compilationError.message : String(compilationError),
      );
    }
  }

  private async performEvaluation(submission: any, assignment: Assignment, sourceCode: string) {
    let testGenTime: number | undefined;
    let testExecTime: number | undefined;
    let summaryTime: number | undefined;

    // Default values
    let testcases: Array<{
      input: string;
      expectedOutput: string;
      tolerance?: number;
    }> = [];
    let testsResult: any[] = [];
    let summary = 'Code submitted successfully';
    let feedback = 'No automated tests were run for this submission.';
    let score = 0;

    if (assignment.testsRequirement?.trim()) {
      // Generate test cases
      const testGenResult = await this.generateTestCases(assignment, sourceCode);
      testcases = testGenResult.testcases;
      testGenTime = testGenResult.testGenTime;

      // Execute tests
      const testExecResult = await this.executeTests(sourceCode, testcases, assignment.language);
      testsResult = testExecResult.testsResult;
      testExecTime = testExecResult.testExecTime;

      // Generate summary
      const summaryResult = await this.generateSummary(sourceCode, testsResult);
      summary = summaryResult.summary;
      feedback = summaryResult.feedback;
      score = summaryResult.score;
      summaryTime = summaryResult.summaryTime;
    }

    return {
      testsResult,
      summary,
      feedback,
      score,
      testGenTime,
      testExecTime,
      summaryTime,
    };
  }

  private async generateTestCases(assignment: Assignment, sourceCode: string) {
    const testGenStartTime = Date.now();
    const prompt =
      `<assignment><title>${assignment.title}</title><description>${assignment.description}</description></assignment><testsRequirement>${assignment.testsRequirement?.trim()}</testsRequirement><sourceCode>${sourceCode.trim()}</sourceCode>`.trim();

    console.log(`[TEST_GEN] Starting test case generation for assignment: ${assignment.title}`);
    console.log(`[TEST_GEN] Prompt length: ${prompt.length} characters`);
    console.log(`[TEST_GEN] Using model: ${getCurrentModelName()}`);

    try {
      const {
        object: { testcases },
      } = await this.testsGeneratorAgent.generate([{ role: 'user', content: prompt }], {
        output: testsGenerationOutputSchema,
        maxTokens: 6000, // Increased token limit for complex test cases
        temperature: 0.1, // Lower temperature for more consistent output
      });

      const testGenTime = Date.now() - testGenStartTime;
      console.log(`[TIMING] Test case generation completed in ${testGenTime}ms`);

      return { testcases, testGenTime };
    } catch (generationError) {
      console.log(`[TEST_GEN] Primary model failed, attempting fallback with Gemini...`);

      // Try with Gemini as fallback
      try {
        const fallbackAgent = createTestsGeneratorAgent(true); // Use Gemini
        const {
          object: { testcases },
        } = await fallbackAgent.generate([{ role: 'user', content: prompt }], {
          output: testsGenerationOutputSchema,
          maxTokens: 6000,
          temperature: 0.1,
        });

        const testGenTime = Date.now() - testGenStartTime;
        console.log(`[TIMING] Test case generation completed with fallback in ${testGenTime}ms`);

        return { testcases, testGenTime };
      } catch (fallbackError) {
        console.error(`[TEST_GEN] Fallback model also failed:`, fallbackError);
      }

      // Both models failed, handle the error
      const testGenTime = Date.now() - testGenStartTime;
      console.log(`[TIMING] Test case generation failed in ${testGenTime}ms`);
      console.error('Test case generation failed:', generationError);

      // Log detailed error information
      console.error(`[TEST_GEN] Error details:`, {
        error: generationError,
        promptLength: prompt.length,
        assignmentTitle: assignment.title,
        hasTestsRequirement: !!assignment.testsRequirement?.trim(),
        sourceCodeLength: sourceCode.length,
      });

      // Check if it's the "No object generated" error specifically
      if (
        generationError instanceof Error &&
        generationError.message.includes('No object generated')
      ) {
        console.error(`[TEST_GEN] Model failed to generate response. This could be due to:`);
        console.error(`[TEST_GEN] - Token limit exceeded`);
        console.error(`[TEST_GEN] - Model API issues`);
        console.error(`[TEST_GEN] - Complex prompt causing model confusion`);
        console.error(`[TEST_GEN] - Schema validation issues`);
      }

      throw new TestGenerationError(
        'Test case generation failed',
        generationError instanceof Error ? generationError.message : String(generationError),
      );
    }
  }

  private async executeTests(sourceCode: string, testcases: any[], language: string) {
    const testExecStartTime = Date.now();
    const languageId = language === 'java' ? 4 : 1;

    try {
      const testsResult = await submitCode({
        languageId,
        submissions: [
          {
            sourceCode: sourceCode || '',
            testcases:
              testcases?.map(({ input, expectedOutput }) => ({
                input: input || '',
                expectedOutput: expectedOutput || '',
              })) || [],
          },
        ],
      });

      const testExecTime = Date.now() - testExecStartTime;
      console.log(`[TIMING] Test execution completed in ${testExecTime}ms`);

      return { testsResult, testExecTime };
    } catch (submitError) {
      const testExecTime = Date.now() - testExecStartTime;
      console.log(`[TIMING] Test execution failed in ${testExecTime}ms`);
      console.error('Test execution failed:', submitError);

      throw new TestExecutionError(
        'Test execution failed',
        submitError instanceof Error ? submitError.message : String(submitError),
      );
    }
  }

  private async generateSummary(sourceCode: string, testsResult: any[]) {
    const summaryStartTime = Date.now();
    const summaryPrompt = `<code>
${sourceCode.trim()}
</code>
<testsResult>${testsResult}</testsResult>`.trim();

    try {
      const {
        object: { summary, feedback, score },
      } = await this.summaryAgent.generate([{ role: 'user', content: summaryPrompt }], {
        output: summaryGenerationOutputSchema,
      });

      const summaryTime = Date.now() - summaryStartTime;
      console.log(`[TIMING] Summary generation completed in ${summaryTime}ms`);

      return { summary, feedback, score, summaryTime };
    } catch (summaryError) {
      const summaryTime = Date.now() - summaryStartTime;
      console.log(`[TIMING] Summary generation failed in ${summaryTime}ms`);
      console.error('Summary generation failed:', summaryError);

      // Return fallback values
      const score =
        testsResult.length > 0
          ? Math.round(
              (testsResult.filter((t) => t.status === 'PASS').length / testsResult.length) * 100,
            )
          : 0;

      return {
        summary: 'Code executed successfully but summary generation failed',
        feedback:
          "Your code was tested successfully, but we couldn't generate detailed feedback. Please review your test results.",
        score,
        summaryTime,
      };
    }
  }

  private async updateSubmission(submissionId: string, result: EvaluationResult) {
    const updateStartTime = Date.now();

    await this.payload.update({
      collection: 'submissions',
      data: {
        testsResult: result.testsResult.map((ts) => ({
          ...ts,
          status: ts.status as 'PASS' | 'FAIL',
        })),
        summary: result.summary,
        score: result.score,
        feedback: result.feedback,
      },
      id: submissionId,
    });

    const updateTime = Date.now() - updateStartTime;
    console.log(`[TIMING] Database update completed in ${updateTime}ms`);

    return { updateTime };
  }

  private async handleCompilationError(submissionId: string, error: CompilationError) {
    await this.payload.update({
      collection: 'submissions',
      data: {
        testsResult: [],
        summary: error.error,
        score: 0,
        feedback:
          'Your code has compilation errors. Please fix the syntax errors and try again. No test cases were generated due to compilation failure.',
      },
      id: submissionId,
    });
  }

  private async handleTestGenerationError(submissionId: string, error: TestGenerationError) {
    await this.payload.update({
      collection: 'submissions',
      data: {
        testsResult: [],
        summary: `Test Generation Error: ${error.error}`,
        score: 0,
        feedback:
          'Failed to generate test cases for your submission. Please try again later or contact support.',
      },
      id: submissionId,
    });
  }

  private async handleTestExecutionError(submissionId: string, error: TestExecutionError) {
    await this.payload.update({
      collection: 'submissions',
      data: {
        testsResult: [],
        summary: `Execution Error: ${error.error}`,
        score: 0,
        feedback:
          'Your code encountered runtime errors during test execution. Please check your logic and try again.',
      },
      id: submissionId,
    });
  }

  private logTimingData(data: TimingData) {
    // This would call the existing writeTimingLogToCSV function
    // For now, just log to console
    console.log('[TIMING DATA]', data);
  }
}

// Custom error classes
class CompilationError extends Error {
  constructor(
    message: string,
    public error: string,
  ) {
    super(message);
    this.name = 'CompilationError';
  }
}

class TestGenerationError extends Error {
  constructor(
    message: string,
    public error: string,
  ) {
    super(message);
    this.name = 'TestGenerationError';
  }
}

class TestExecutionError extends Error {
  constructor(
    message: string,
    public error: string,
  ) {
    super(message);
    this.name = 'TestExecutionError';
  }
}
