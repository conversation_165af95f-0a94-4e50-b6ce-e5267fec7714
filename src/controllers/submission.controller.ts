import archiver from "archiver";
import { createWriteStream } from "fs";
import { access, mkdir, readFile, rm, writeFile } from "fs/promises";
import path from "path";
import type { BasePayload, PayloadRequest } from "payload";
import { z } from "zod";
import type { Submission } from "@/payload-types";
import { SubmissionDataSchema } from "../schemas/assignment.schemas";
import { AssignmentService } from "../services/assignment.service";
import { SubmissionService } from "../services/submission.service";
import { extractTenantId } from "../utils/tenant.utils";

interface ParsedSubmission {
	id: string;
	status: string;
	// score: number;
	studentId: string;
	// passedTestCases: number;
	// failedTestCases: number;
	html?: string;
	css?: string;
	js?: string;
	java?: string;
	c?: string;
	language: string;
}

interface DownloadConfig {
	tempDir: string;
}

export class SubmissionController {
	private static readonly config: DownloadConfig = {
		tempDir: process.env.TEMP_DIR || "temp",
	};

	static async evaluateSubmission({ routeParams, payload }: PayloadRequest) {
		const { id } = routeParams ?? {};

		if (!id) {
			return Response.json(
				{ message: "Invalid submission ID" },
				{ status: 400 },
			);
		}

		const createdJob = await payload.jobs.queue({
			task: "submission-worker",
			input: {
				submissionId: id.toString(),
			},
		});

		try {
			payload.jobs.runByID({
				id: createdJob.id,
			});

			return Response.json({
				message: "Started evaluation job for submission " + id,
				jobId: createdJob.id,
			});
		} catch (error) {
			console.error("Submission error:", error);
			return Response.json({
				message: "An error occurred while processing your submission",
			});
		}
	}

	static async handleSubmission({
		user,
		routeParams,
		json,
		payload,
	}: PayloadRequest) {
		try {
			// Validate input
			const rawData = json ? await json() : {};
			const validatedData = SubmissionDataSchema.parse(rawData);

			console.log(validatedData);

			const id = parseInt((routeParams?.id as string) || "0");
			if (!id || isNaN(id)) {
				return Response.json(
					{ message: "Invalid assignment ID" },
					{ status: 400 },
				);
			}

			const userId = String(user?.id);
			if (!userId) {
				return Response.json(
					{ message: "User not authenticated" },
					{ status: 401 },
				);
			}

			const tenantId = extractTenantId(user);

			// Initialize services
			const assignmentService = new AssignmentService(payload);
			const submissionService = new SubmissionService(payload);

			// Check if assignment exists
			const assignment = await assignmentService.findById(id);
			if (!assignment) {
				return Response.json(
					{ message: "Assignment not found" },
					{ status: 404 },
				);
			}

			// Check for existing submission
			const submissions = await submissionService.findExistingSubmission(
				id,
				userId,
				tenantId,
			);

			// Calculate score
			// const { passedTestCases, failedTestCases } = validatedData;
			// const totalTestCases = passedTestCases + failedTestCases;
			// const score = totalTestCases > 0 ? Math.floor((passedTestCases / totalTestCases) * 100) : 0;

			let code = "";
			switch (assignment.language) {
				case "c":
					code = validatedData.c ?? "";
					break;
				case "java":
					code = validatedData.java ?? "";
					break;
			}

			if (submissions.totalDocs > 0) {
				// check if the code is same or not
				const solutionCode =
					submissions?.docs[0]?.[
						assignment.language as keyof (typeof submissions.docs)[0]
					];
				if (solutionCode && code.trim() === (solutionCode as string).trim()) {
					return Response.json(
						{ message: "This code is identical to your last submission." },
						{ status: 400 },
					);
				}

				// Update existing submission
				await submissionService.updateSubmission(
					submissions.docs[0].id,
					validatedData,
					id,
					userId,
					tenantId,
				);

				return Response.json({
					message: "Assignment updated successfully",
					// score,
					status: "updated",
				});
			}

			// Create new submission
			const newSubmission = await submissionService.createSubmission(
				validatedData,
				id,
				userId,
				tenantId,
			);

			return Response.json({
				message: "Assignment submitted successfully",
				// score,
				status: "created",
				submissionId: newSubmission.id,
			});
		} catch (error) {
			console.error("Submission error:", error);

			if (error instanceof z.ZodError) {
				return Response.json(
					{
						message: "Invalid submission data",
						errors: error.issues.join(", "),
					},
					{ status: 400 },
				);
			}

			return Response.json(
				{
					message:
						(error instanceof Error ? error.message : error) ||
						"An error occurred while processing your submission",
				},
				{ status: 500 },
			);
		}
	}

	static async handleDownloadReport({
		user,
		routeParams,
		payload,
	}: PayloadRequest) {
		let tempFolderPath: string | null = null;
		let zipFilePath: string | null = null;

		try {
			// Validate input
			const id = parseInt((routeParams?.id as string) || "0");
			if (!id || isNaN(id)) {
				return Response.json(
					{ message: "Invalid assignment ID" },
					{ status: 400 },
				);
			}

			const userId = String(user?.id);
			if (!userId) {
				return Response.json(
					{ message: "User not authenticated" },
					{ status: 401 },
				);
			}

			const tenantId = extractTenantId(user);

			if (!id || isNaN(id)) {
				return Response.json(
					{ message: "Invalid assignment ID" },
					{ status: 400 },
				);
			}

			if (!userId) {
				return Response.json(
					{ message: "User not authenticated" },
					{ status: 401 },
				);
			}

			if (!tenantId) {
				return Response.json(
					{ message: "Tenant not authenticated" },
					{ status: 401 },
				);
			}

			// Fetch assignment and submissions
			const { assignment, submissions } =
				await SubmissionController.fetchAssignmentData(id, payload);

			if (!assignment) {
				return Response.json(
					{ message: "Assignment not found" },
					{ status: 404 },
				);
			}

			if (submissions.totalDocs === 0) {
				return Response.json(
					{ message: "No submissions found" },
					{ status: 404 },
				);
			}

			// Parse submissions
			const parsedSubmissions = SubmissionController.parseSubmissions(
				assignment.language,
				submissions.docs,
			);

			// Create temporary folder and write files
			const folderName = SubmissionController.sanitizeFolderName(
				id,
				assignment.title,
			);
			tempFolderPath = path.resolve(
				SubmissionController.config.tempDir,
				folderName,
			);

			await SubmissionController.createSubmissionFiles(
				tempFolderPath,
				parsedSubmissions,
			);

			// Create zip file
			const zipFileName = `${folderName}-submissions.zip`;
			zipFilePath = path.resolve(
				SubmissionController.config.tempDir,
				zipFileName,
			);
			await SubmissionController.createZipFile(tempFolderPath, zipFilePath);

			// Read zip file and return as response
			const zipBuffer = await readFile(zipFilePath);

			// Return the zip file as response
			return new Response(zipBuffer, {
				status: 200,
				headers: {
					"Content-Type": "application/zip",
					"Content-Disposition": `attachment; filename="${zipFileName}"`,
					"Content-Length": zipBuffer.length.toString(),
				},
			});
		} catch (error) {
			console.error("Download error:", error);
			return Response.json(
				{ message: "An error occurred while processing the download" },
				{ status: 500 },
			);
		} finally {
			// Cleanup temporary files
			if (tempFolderPath) {
				await SubmissionController.cleanup(tempFolderPath);
			}
			if (zipFilePath) {
				await SubmissionController.cleanup(zipFilePath);
			}
		}
	}

	private static async fetchAssignmentData(id: number, payload: BasePayload) {
		const assignmentService = new AssignmentService(payload);
		const submissionService = new SubmissionService(payload);

		const [assignment, submissions] = await Promise.all([
			assignmentService.findById(id),
			submissionService.findSubmissionsById(id),
		]);

		return { assignment, submissions };
	}

	private static parseSubmissions(
		language: string,
		submissions: Submission[],
	): ParsedSubmission[] {
		return submissions.map((submission) => {
			// const passedTestCases = (submission.passedTestCases ?? 0) as number;
			// const failedTestCases = (submission.failedTestCases ?? 0) as number;
			// const totalTestCases = passedTestCases + failedTestCases;
			// const score = totalTestCases > 0 ? Math.floor((passedTestCases / totalTestCases) * 100) : 0;
			return {
				id: String(submission.id),
				status: submission.status as string,
				// score,
				studentId: SubmissionController.sanitizeFileName(
					String(submission.student),
				),
				// passedTestCases,
				// failedTestCases,
				html: submission.html || "",
				css: submission.css || "",
				js: submission.js || "",
				java: submission.java || "",
				c: submission.c || "",
				language,
			};
		});
	}

	private static sanitizeFolderName(id: number, title: string): string {
		const sanitizedTitle = title
			.trim()
			.toLowerCase()
			.replace(/[^a-zA-Z0-9\s-]/g, "")
			.replace(/\s+/g, "-")
			.substring(0, 50); // Limit length

		return `${id}-${sanitizedTitle}`;
	}

	private static sanitizeFileName(fileName: string): string {
		return String(fileName).replace(/[^a-zA-Z0-9.-]/g, "_");
	}

	private static async createSubmissionFiles(
		folderPath: string,
		submissions: ParsedSubmission[],
	): Promise<void> {
		// Create directory
		await mkdir(folderPath, { recursive: true });

		// Write all files concurrently
		const writePromises = submissions.map(async (submission) => {
			const fileName = `${submission.studentId}.${submission.language}`;
			const filePath = path.join(folderPath, fileName);
			const fileContent =
				submission[submission.language as keyof typeof submission] || "";

			await writeFile(filePath, fileContent, "utf8");
		});

		await Promise.all(writePromises);
	}

	private static async createZipFile(
		sourceFolderPath: string,
		zipFilePath: string,
	): Promise<void> {
		return new Promise((resolve, reject) => {
			// Ensure temp directory exists
			mkdir(path.dirname(zipFilePath), { recursive: true })
				.then(() => {
					const output = createWriteStream(zipFilePath);
					const archive = archiver("zip", {
						zlib: { level: 9 }, // Maximum compression
					});

					output.on("close", () => {
						resolve();
					});

					archive.on("error", (err) => {
						reject(err);
					});

					archive.pipe(output);

					// Add all files from the source folder to the zip
					archive.directory(sourceFolderPath, false);

					archive.finalize();
				})
				.catch(reject);
		});
	}

	private static async cleanup(filePath: string): Promise<void> {
		try {
			// Check if path exists before attempting to remove
			await access(filePath);
			await rm(filePath, { recursive: true, force: true });
		} catch (error) {
			// Ignore cleanup errors, just log them
			console.warn(
				`Cleanup warning: ${error instanceof Error ? error.message : "Unknown error"}`,
			);
		}
	}
}
