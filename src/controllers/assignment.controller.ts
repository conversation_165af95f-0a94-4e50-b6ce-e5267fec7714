import type { PayloadRequest } from "payload";
import { z } from "zod";
import { mastra } from "@/mastra";
import type { GeneratedAssignmentContent } from "@/types/assignment";
import { GenerateAssignmentSchema } from "../schemas/assignment.schemas";
import { AssignmentService } from "../services/assignment.service";
import { extractTenantId } from "../utils/tenant.utils";

export class AssignmentController {
	static async handleGeneration({ user, json, payload }: PayloadRequest) {
		try {
			// Validate input
			const rawData = json ? await json() : {};
			const validatedData = GenerateAssignmentSchema.parse(rawData);

			if (!user?.id) {
				return Response.json(
					{ message: "User not authenticated" },
					{ status: 401 },
				);
			}

			const tenantId = extractTenantId(user);

			if (!tenantId) {
				return Response.json(
					{ message: "Tenant not authenticated" },
					{ status: 401 },
				);
			}

			const run = mastra.getWorkflow("assignmentWorkflow").createRun();

			const language = validatedData.language as "java" | "c";

			const result = await run.start({
				inputData: {
					topic: validatedData.title,
					difficulty: validatedData.difficulty,
					language: language,
					additionalRequirements: validatedData.additionalRequirements,
				},
			});

			if (result.status === "failed") {
				return Response.json(
					{ message: "Failed to generate assignment" },
					{ status: 500 },
				);
			}

			if (result.status === "suspended") {
				return Response.json(
					{ message: "Generation suspended" },
					{ status: 400 },
				);
			}

			const { steps } = result;

			const { output } = steps["generate-testcases"] as { output: any };

			const generatedContent: GeneratedAssignmentContent = {
				title: output.title,
				language: language,
				subject: validatedData.subjectId,
				difficulty: validatedData.difficulty,
				description: output.description,
				points: validatedData.points,
				instructions: output.instructions.replace(/^# [^\n]*\n?/, ""),
				starterCode: output.starterCode,
				solutionCode: output.solutionCode,
				testsRequirement: output?.testsRequirement || [],
				// requiresCommandLineArgs: output?.requiresCommandLineArgs || false,
				hints: output.hints,
				resources: output.resources,
			};

			// TODO: REMOVE THIS DEPRECATED CODE
			// Generate content via AI
			// const generatedContent = await AIGeneratorService.generateAssignmentContent({
			//   title: validatedData.title,
			//   difficulty: validatedData.difficulty,
			//   points: validatedData.points,
			//   additionalRequirements: validatedData.additionalRequirements,
			//   generationOptions: validatedData.generationOptions,
			// });

			// console.log(generatedContent);

			// Create assignment
			const assignmentService = new AssignmentService(payload);
			const createdAssignment = await assignmentService.createAssignment(
				generatedContent,
				validatedData.difficulty,
				tenantId,
			);

			return Response.json({
				message: "Assignment generated successfully",
				assignment: {
					id: createdAssignment.id,
					title: createdAssignment.title,
					description: createdAssignment.description,
					points: createdAssignment.points,
					difficulty: createdAssignment.difficulty,
					dueDate: createdAssignment.dueDate,
				},
			});
		} catch (error) {
			console.error("Assignment generation error:", error);

			if (error instanceof z.ZodError) {
				return Response.json(
					{
						message: "Invalid input data",
						errors: error.issues.join(", "),
					},
					{ status: 400 },
				);
			}

			return Response.json(
				{
					message:
						error instanceof Error
							? error.message
							: "Failed to generate assignment",
				},
				{ status: 500 },
			);
		}
	}
}
