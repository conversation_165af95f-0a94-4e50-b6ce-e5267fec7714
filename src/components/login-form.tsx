'use client';

import { useForm } from '@tanstack/react-form';
import { Loader2Icon } from 'lucide-react';
import Link from 'next/link';
import { useSearchParams } from 'next/navigation';
import type React from 'react';
import { useEffect, useRef, useState } from 'react';
import { toast } from 'sonner';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { PasswordInput } from '@/components/ui/password-input';
import { cn } from '@/lib/utils';
import { Footer } from './footer';

// Validation functions
const validateEmail = (email: string): string | null => {
  if (!email || !email.trim()) {
    return 'Email is required';
  }
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  if (!emailRegex.test(email)) {
    return 'Please enter a valid email address';
  }
  return null;
};

const validatePassword = (password: string): string | null => {
  if (!password || !password.trim()) {
    return 'Password is required';
  }
  if (password.length < 4) {
    return 'Password must be at least 4 characters long';
  }
  return null;
};

// Custom inline error component with inline CSS
const InlineError = ({ error }: { error: string | null }) => {
  if (!error) return null;

  return (
    <div
      style={{
        color: '#dc2626',
        fontSize: '0.875rem',
        marginTop: '0.25rem',
        lineHeight: '1.25rem',
      }}
    >
      {error}
    </div>
  );
};

// General error component
const GeneralError = ({ error }: { error: string | null }) => {
  if (!error) return null;
  return (
    <div
      style={{
        color: '#dc2626',
        fontSize: '0.95rem',
        marginTop: '0.5rem',
        marginBottom: '0.5rem',
        textAlign: 'center',
        fontWeight: 500,
      }}
    >
      {error}
    </div>
  );
};

export function LoginForm({ className, ...props }: React.ComponentProps<'div'>) {
  const searchParams = useSearchParams();
  const redirectToPath = searchParams.get('redirect');
  const errorParam = searchParams.get('error');

  // State for server-side errors
  const [serverErrors, setServerErrors] = useState<{
    email?: string;
    password?: string;
    general?: string;
  }>({});

  // Refs for autofill detection
  const emailInputRef = useRef<HTMLInputElement>(null);
  const passwordInputRef = useRef<HTMLInputElement>(null);

  // Helper function to check if user has a specific role
  const hasRole = (userRoles: string[], role: string) => {
    return userRoles?.includes(role);
  };

  // Helper function to get default redirect based on user role
  const getDefaultRedirect = (userRoles: string[]) => {
    if (hasRole(userRoles, 'faculty')) {
      return '/admin';
    } else if (hasRole(userRoles, 'student')) {
      return '/subjects';
    }
    return '/';
  };

  // Helper function to determine if user can access a path
  const canAccessPath = (path: string, userRoles: string[]) => {
    if (path.startsWith('/admin')) {
      return hasRole(userRoles, 'faculty');
    }
    return true;
  };

  // Autofill support: on mount, check if browser auto-filled values and clear errors if valid
  useEffect(() => {
    const email = emailInputRef.current?.value || '';
    const password = passwordInputRef.current?.value || '';
    if (!validateEmail(email) && !validatePassword(password)) {
      setServerErrors({});
    }
  }, []);

  const form = useForm({
    defaultValues: {
      email: '',
      password: '',
    },
    validators: {
      onSubmit: ({ value }) => {
        const emailError = validateEmail(value.email);
        const passwordError = validatePassword(value.password);
        if (emailError || passwordError) {
          return {
            fields: {
              ...(emailError && { email: emailError }),
              ...(passwordError && { password: passwordError }),
            },
          };
        }
        return undefined;
      },
    },
    onSubmit: async ({ value }) => {
      setServerErrors({}); // Clear previous server errors
      try {
        const res = await fetch('/api/users/login', {
          method: 'POST',
          credentials: 'include',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(value),
        });

        if (!res.ok) {
          let errorMessage = 'Invalid email or password';
          try {
            const errorData = await res.json();
            errorMessage = errorData.message || errorMessage;
          } catch {
            // Failed to parse error response, use default message
          }
          setServerErrors({ general: errorMessage });
          return;
        }

        const userData = await res.json();
        toast.success('Login successful');

        setTimeout(() => {
          const userRoles = userData.user?.roles || userData.roles || [];
          let finalRedirect = '/';

          if (errorParam === 'not-faculty' && redirectToPath?.startsWith('/admin')) {
            finalRedirect = '/';
          } else if (redirectToPath && canAccessPath(redirectToPath, userRoles)) {
            finalRedirect = redirectToPath;
          } else {
            finalRedirect = getDefaultRedirect(userRoles);
          }

          if (typeof window !== 'undefined') {
            window.location.href = finalRedirect;
          }
        }, 100);
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : 'Something went wrong';
        setServerErrors({ general: errorMessage });
      }
    },
  });

  // Clear all errors on any field change
  const handleAnyFieldChange = () => {
    if (serverErrors.general || serverErrors.email || serverErrors.password) {
      setServerErrors({});
    }
  };

  return (
    <div className={cn('flex flex-col gap-6', className)} {...props}>
      <form
        onSubmit={(e) => {
          e.preventDefault();
          e.stopPropagation();
          form.handleSubmit();
        }}
        className="space-y-4"
        autoComplete="on"
      >
        <div className="flex flex-col items-center gap-2 text-center pb-3">
          <h1 className="text-2xl font-bold">Login to your account</h1>
          <p className="text-balance text-sm text-muted-foreground">
            Enter your email below to login to your account
          </p>
        </div>

        {/* General error area */}
        <GeneralError error={serverErrors.general || null} />

        <div className="grid gap-4">
          <form.Field
            name="email"
            validators={{
              onBlur: ({ value }) => validateEmail(value),
              // Only validate on blur, not on every change
            }}
          >
            {(field) => (
              <div className="grid gap-2">
                <Label htmlFor={field.name} className="text-sm font-medium text-foreground">
                  Email
                </Label>
                <Input
                  ref={emailInputRef}
                  placeholder="Enter your email"
                  name={field.name}
                  type="email"
                  value={field.state.value}
                  onBlur={field.handleBlur}
                  onChange={(e) => {
                    handleAnyFieldChange();
                    field.handleChange(e.target.value);
                  }}
                  className="h-11"
                  autoComplete="email"
                />
                <InlineError error={field.state.meta.errors?.[0] || null} />
              </div>
            )}
          </form.Field>
          <form.Field
            name="password"
            validators={{
              onBlur: ({ value }) => validatePassword(value),
              // Only validate on blur, not on every change
            }}
          >
            {(field) => (
              <div className="grid gap-2">
                <div className="flex items-center justify-between">
                  <Label htmlFor={field.name} className="text-sm font-medium text-foreground">
                    Password
                  </Label>
                </div>
                <PasswordInput
                  ref={passwordInputRef}
                  placeholder="Enter your password"
                  name={field.name}
                  value={field.state.value}
                  onBlur={field.handleBlur}
                  onChange={(e) => {
                    handleAnyFieldChange();
                    field.handleChange(e.target.value);
                  }}
                  className="h-11"
                  autoComplete="current-password"
                />
                <InlineError error={field.state.meta.errors?.[0] || null} />
              </div>
            )}
          </form.Field>
          <form.Subscribe selector={(state) => [state.canSubmit, state.isSubmitting]}>
            {([canSubmit, isSubmitting]) => (
              <Button
                type="submit"
                className="w-full"
                size="lg"
                disabled={!canSubmit || isSubmitting}
                tabIndex={0}
              >
                {isSubmitting ? (
                  <Loader2Icon className={cn('size-4 animate-spin opacity-100')} />
                ) : (
                  'Login'
                )}
              </Button>
            )}
          </form.Subscribe>
        </div>
      </form>

      <div className="text-center text-xs text-muted-foreground space-y-3 pt-4 border-t">
        <p>
          By signing in, you agree to our{' '}
          <Link
            href="/terms"
            className="text-primary hover:text-primary/80 underline underline-offset-2 transition-colors"
          >
            Terms
          </Link>{' '}
          and{' '}
          <Link
            href="/privacy"
            className="text-primary hover:text-primary/80 underline underline-offset-2 transition-colors"
          >
            Privacy Policy
          </Link>
        </p>
        <Footer variant="small" />
      </div>
    </div>
  );
}
